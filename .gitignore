# Build artifacts that increase bundle size
*.log
*.tmp
.expo/
.expo-shared/
dist/
build/
android/app/build/
ios/build/
node_modules/

# Environment and sensitive files
.env
.env.*
!.env.example
*.key
*.pem
google-services.json
GoogleService-Info.plist

# Large development files
*.psd
*.ai
*.sketch
*.fig
*.docx
*.pdf

# Temporary files
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# IDE files
.vscode/
.idea/
*.sublime-*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli