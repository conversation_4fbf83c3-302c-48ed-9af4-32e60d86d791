rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // ========================================
    // HELPER FUNCTIONS
    // ========================================
    
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && request.auth.uid != null;
    }
    
    // Safe helper functions that don't use get() to avoid failures
    function isChatParticipantSafe() {
      return isAuthenticated() && 
             (request.auth.uid in resource.data.participants ||
              request.auth.uid in request.resource.data.participants);
    }
    
    function isGroupMemberSafe() {
      return isAuthenticated() && 
             (request.auth.uid in resource.data.members ||
              request.auth.uid in request.resource.data.members ||
              request.auth.uid in resource.data.participants ||
              request.auth.uid in request.resource.data.participants);
    }
    
    // ========================================
    // CORE USER & AUTHENTICATION
    // ========================================
    
    // Users collection - Allow reading for contact discovery, writing own profile
    match /users/{userId} {
      allow read: if true; // Public for contact discovery
      allow write, create, update, delete: if isOwner(userId);
    }
    
    // User profiles - Public read, owner write
    match /userProfiles/{userId} {
      allow read: if true; // Public profile discovery
      allow write, create, update, delete: if isOwner(userId);
    }
    
    // Contacts - User's own contacts
    match /contacts/{contactId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // Online status - Owner write, all read
    match /onlineStatus/{userId} {
      allow read: if isAuthenticated();
      allow write, create, update, delete: if isOwner(userId);
    }
    
    // ========================================
    // MESSAGING & CHATS
    // ========================================
    
    // Chats - Participants only
    match /chats/{chatId} {
      allow read, write, update: if isChatParticipantSafe();
      allow create: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Messages in chats
    match /chats/{chatId}/messages/{messageId} {
      allow read, write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Groups - Members only
    match /groups/{groupId} {
      allow read, write, update: if isGroupMemberSafe();
      allow create: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Group messages
    match /groups/{groupId}/messages/{messageId} {
      allow read, write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Typing indicators
    match /typing/{chatId}/users/{userId} {
      allow read: if isAuthenticated();
      allow write, create, update, delete: if isOwner(userId);
    }

    // ========================================
    // CALLS & VIDEO FEATURES
    // ========================================

    // Call logs
    match /callLogs/{callId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Video calls
    match /videoCalls/{callId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Voice calls
    match /voiceCalls/{callId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Call history
    match /callHistory/{historyId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // ========================================
    // SEARCH & DISCOVERY
    // ========================================

    // Search history
    match /searchHistory/{searchId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Search suggestions
    match /searchSuggestions/{suggestionId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Search analytics
    match /searchAnalytics/{analyticsId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // User discovery
    match /userDiscovery/{discoveryId} {
      allow read: if true; // Public for discovery
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // ========================================
    // MEDIA & FILES
    // ========================================
    
    // Media files
    match /media/{mediaId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Documents
    match /documents/{documentId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Voice messages
    match /voiceMessages/{voiceId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // ========================================
    // SOCIAL FEATURES (TIKTOK-STYLE)
    // ========================================
    
    // Updates/Posts - Public read, authenticated write
    match /updates/{updateId} {
      allow read: if true; // Public content discovery
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Update views
    match /updateViews/{viewId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Update likes
    match /updateLikes/{likeId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Update comments
    match /updateComments/{commentId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Update shares
    match /updateShares/{shareId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Update saves - Owner only
    match /updateSaves/{saveId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Update downloads - Track downloaded updates
    match /updateDownloads/{downloadId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Update download history - User's download history
    match /updateDownloadHistory/{historyId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Update download queue - Pending downloads
    match /updateDownloadQueue/{queueId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Update download analytics - Track download metrics
    match /updateDownloadAnalytics/{analyticsId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // User updates grid
    match /userUpdatesGrid/{gridId} {
      allow read: if true; // Public content discovery
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Profile highlights
    match /profileHighlights/{highlightId} {
      allow read: if true; // Public content discovery
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Profile views
    match /profileViews/{viewId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // ========================================
    // INTERACTIONS & REACTIONS
    // ========================================
    
    // Reactions
    match /reactions/{reactionId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Full screen interactions
    match /fullScreenInteractions/{interactionId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Video autoplay analytics
    match /videoAutoplayAnalytics/{analyticsId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // ========================================
    // SCROLL & NAVIGATION ANALYTICS
    // ========================================

    // Scroll tracking - Track user scroll behavior
    match /scrollTracking/{scrollId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Navigation analytics - Track app navigation patterns
    match /navigationAnalytics/{navId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Page views - Track page/screen visits
    match /pageViews/{viewId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Screen time tracking
    match /screenTime/{timeId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // User sessions - Track app usage sessions
    match /userSessions/{sessionId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Scroll positions - Save scroll positions for continuity
    match /scrollPositions/{positionId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Navigation history - User's navigation history
    match /navigationHistory/{historyId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // ========================================
    // NOTIFICATIONS & SYSTEM
    // ========================================

    // Notifications - Owner only
    match /notifications/{notificationId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Push notifications
    match /pushNotifications/{pushId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Notification settings
    match /notificationSettings/{settingsId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Device tokens for push notifications
    match /deviceTokens/{tokenId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // App settings - User preferences
    match /appSettings/{settingsId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // User preferences
    match /userPreferences/{prefId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Theme settings
    match /themeSettings/{themeId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Privacy settings
    match /privacySettings/{privacyId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Account settings
    match /accountSettings/{accountId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // ========================================
    // DOWNLOAD & DELETE FEATURES
    // ========================================

    // Downloads - Owner only (general downloads)
    match /downloads/{downloadId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Download queue (general download queue)
    match /downloadQueue/{queueId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Media downloads - Specific to media files
    match /mediaDownloads/{downloadId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Document downloads - Specific to document files
    match /documentDownloads/{downloadId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Voice message downloads
    match /voiceDownloads/{downloadId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Download progress tracking
    match /downloadProgress/{progressId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Download statistics
    match /downloadStats/{statsId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // Deleted messages
    match /deletedMessages/{deletedId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // Deleted media
    match /deletedMedia/{deletedId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // Deleted chats
    match /deletedChats/{deletedId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // ========================================
    // DEVELOPMENT & TESTING
    // ========================================
    
    // Test collections for development
    match /test_messages/{document} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    match /test/{document=**} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // Development collections
    match /dev/{document=**} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // ========================================
    // ADDITIONAL APP FEATURES
    // ========================================

    // Stories (like Instagram/WhatsApp stories)
    match /stories/{storyId} {
      allow read: if true; // Public stories
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Story views
    match /storyViews/{viewId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Bookmarks
    match /bookmarks/{bookmarkId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Favorites
    match /favorites/{favoriteId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Recent activity
    match /recentActivity/{activityId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // User activity logs
    match /activityLogs/{logId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // App usage statistics
    match /usageStats/{statsId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Error logs (for debugging)
    match /errorLogs/{errorId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Feedback and reports
    match /feedback/{feedbackId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // User reports (for content moderation)
    match /userReports/{reportId} {
      allow read: if isAuthenticated();
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // Blocked users
    match /blockedUsers/{blockId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Muted users/chats
    match /mutedUsers/{muteId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // App cache data
    match /cacheData/{cacheId} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
    
    // ========================================
    // FALLBACK RULES
    // ========================================
    
    // Allow authenticated users access to any other collections
    // This ensures the app doesn't break if new collections are added
    match /{document=**} {
      allow read, write, create, update, delete: if isAuthenticated();
    }
  }
}
