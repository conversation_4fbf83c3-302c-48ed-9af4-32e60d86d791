rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // ===== HELPER FUNCTIONS =====
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             request.auth.uid != null && 
             request.auth.uid.size() > 0;
    }
    
    function isChatParticipant(chatId) {
      return isAuthenticated() && 
             request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
    }
    
    function isGroupMember(groupId) {
      return isAuthenticated() && 
             request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.members;
    }
    
    function isGroupAdmin(groupId) {
      return isAuthenticated() && 
             request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.admins;
    }
    
    function isBlocked(targetUserId) {
      return exists(/databases/$(database)/documents/blocks/$(request.auth.uid)) &&
             targetUserId in get(/databases/$(database)/documents/blocks/$(request.auth.uid)).data.blockedUsers;
    }
    
    // ===== CORE USER MANAGEMENT =====
    
    // Users collection - users can read/write their own data + basic info of others
    match /users/{userId} {
      allow read: if isAuthenticated() && (
        isOwner(userId) || 
        resource.data.keys().hasAny(['displayName', 'avatar', 'status', 'isOnline', 'name', 'username'])
      );
      allow write: if isAuthenticated() && isOwner(userId);
      allow create: if isAuthenticated() && isOwner(userId) && isValidUser();
    }
    
    // Contacts collection - users can read/write their own contacts
    match /contacts/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Blocked users collection - users can manage their own blocks
    match /blocks/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // User analytics - users can read their own analytics
    match /userAnalytics/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if false; // Only server writes analytics
    }
    
    // ===== MESSAGING SYSTEM =====
    
    // Chats collection - participants can read/write
    match /chats/{chatId} {
      allow read: if isAuthenticated() && 
                     request.auth.uid in resource.data.participants;
      allow write: if isAuthenticated() && 
                      request.auth.uid in resource.data.participants;
      allow create: if isAuthenticated() && 
                       request.auth.uid in request.resource.data.participants;
    }
    
    // Messages subcollection within chats
    match /chats/{chatId}/messages/{messageId} {
      allow read: if isAuthenticated() && isChatParticipant(chatId);
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.senderId) &&
                       isChatParticipant(chatId);
      allow update: if isAuthenticated() && 
                       isOwner(resource.data.senderId) &&
                       request.resource.data.senderId == resource.data.senderId;
      allow delete: if isAuthenticated() && isOwner(resource.data.senderId);
    }
    
    // Typing indicators subcollection
    match /chats/{chatId}/typing/{userId} {
      allow read: if isAuthenticated() && isChatParticipant(chatId);
      allow write: if isAuthenticated() && isOwner(userId) && isChatParticipant(chatId);
    }
    
    // Message status tracking
    match /messageStatus/{messageId} {
      allow read: if isAuthenticated() && (
        isOwner(resource.data.senderId) || 
        request.auth.uid in resource.data.recipients
      );
      allow write: if isAuthenticated() && (
        isOwner(resource.data.senderId) || 
        request.auth.uid in resource.data.recipients
      );
    }
    
    // Message reactions
    match /messageReactions/{reactionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // ===== GROUP MANAGEMENT =====
    
    // Groups collection - members can read, admins can write
    match /groups/{groupId} {
      allow read: if isAuthenticated() && isGroupMember(groupId);
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.createdBy) &&
                       request.auth.uid in request.resource.data.members;
      allow update: if isAuthenticated() && isGroupAdmin(groupId);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }
    
    // Group messages subcollection
    match /groups/{groupId}/messages/{messageId} {
      allow read: if isAuthenticated() && isGroupMember(groupId);
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.senderId) &&
                       isGroupMember(groupId);
      allow update: if isAuthenticated() && isOwner(resource.data.senderId);
      allow delete: if isAuthenticated() && (
        isOwner(resource.data.senderId) || 
        isGroupAdmin(groupId)
      );
    }
    
    // ===== MEDIA & FILES =====
    
    // Media collection - authenticated users can read, uploaders can write
    match /media/{mediaId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.uploadedBy);
      allow update: if isAuthenticated() && isOwner(resource.data.uploadedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.uploadedBy);
    }
    
    // Documents collection - authenticated users can read, uploaders can write
    match /documents/{documentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.uploadedBy);
      allow update: if isAuthenticated() && isOwner(resource.data.uploadedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.uploadedBy);
    }
    
    // Voice messages collection
    match /voiceMessages/{voiceId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.senderId);
      allow update: if isAuthenticated() && isOwner(resource.data.senderId);
      allow delete: if isAuthenticated() && isOwner(resource.data.senderId);
    }
    
    // ===== SOCIAL FEATURES (UPDATES/STORIES) =====
    
    // Updates collection - users can read all, write their own
    match /updates/{updateId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // Update views tracking
    match /updateViews/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // Update likes tracking
    match /updateLikes/{likeId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // Update comments subcollection
    match /updates/{updateId}/comments/{commentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && (
        isOwner(resource.data.userId) || 
        isOwner(get(/databases/$(database)/documents/updates/$(updateId)).data.userId)
      );
    }
    
    // ===== CALLING SYSTEM =====
    
    // Calls collection - participants can read/write
    match /calls/{callId} {
      allow read, write: if isAuthenticated() && (
        isOwner(resource.data.callerId) || 
        isOwner(resource.data.receiverId) ||
        request.auth.uid in resource.data.participants
      );
      allow create: if isAuthenticated() && (
        isOwner(request.resource.data.callerId) || 
        request.auth.uid in request.resource.data.participants
      );
    }
    
    // Group calls collection
    match /groupCalls/{callId} {
      allow read, write: if isAuthenticated() && 
                            request.auth.uid in resource.data.participants;
      allow create: if isAuthenticated() && 
                       request.auth.uid in request.resource.data.participants;
    }
    
    // Call logs - users can read their own call history
    match /callLogs/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Active calls - participants can read/write
    match /activeCalls/{callId} {
      allow read, write: if isAuthenticated() && (
        request.auth.uid in resource.data.participants ||
        isOwner(resource.data.initiator)
      );
    }
    
    // ===== STATUS & PRESENCE =====
    
    // Online status - users can read all, write their own
    match /onlineStatus/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(userId);
    }
    
    // Last seen - users can read all, write their own
    match /lastSeen/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(userId);
    }
    
    // ===== NOTIFICATIONS =====
    
    // Notifications collection - users can read/write their own
    match /notifications/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // ===== SECURITY & PRIVACY =====
    
    // Encryption keys - users can read/write their own
    match /encryptionKeys/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Phone verification - users can read/write their own
    match /phoneVerification/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Reported content - users can create reports, read their own
    match /reportedContent/{reportId} {
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.reporterId) &&
                       isValidUser();
      allow read: if isAuthenticated() && isOwner(resource.data.reporterId);
    }
    
    // ===== SETTINGS & PREFERENCES =====

    // User settings - users can read/write their own
    match /settings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ===== ADDITIONAL COLLECTIONS =====

    // Downloads tracking
    match /downloads/{downloadId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Deleted messages tracking
    match /deletedMessages/{messageId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.deletedBy);
      allow create: if isAuthenticated() && isOwner(request.resource.data.deletedBy);
    }

    // Deleted media tracking
    match /deletedMedia/{mediaId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.deletedBy);
      allow create: if isAuthenticated() && isOwner(request.resource.data.deletedBy);
    }

    // Deleted chats tracking
    match /deletedChats/{chatId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.deletedBy);
      allow create: if isAuthenticated() && isOwner(request.resource.data.deletedBy);
    }

    // Download queue
    match /downloadQueue/{queueId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // User profiles extended
    match /userProfiles/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(userId);
    }

    // Profile views tracking
    match /profileViews/{viewId} {
      allow read: if isAuthenticated() && (
        isOwner(resource.data.viewerId) ||
        isOwner(resource.data.profileUserId)
      );
      allow create: if isAuthenticated() && isOwner(request.resource.data.viewerId);
    }

    // User updates grid
    match /userUpdatesGrid/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(userId);
    }

    // Update shares tracking
    match /updateShares/{shareId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Update saves tracking
    match /updateSaves/{saveId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Full screen interactions
    match /fullScreenInteractions/{interactionId} {
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Video autoplay analytics
    match /videoAutoplayAnalytics/{analyticsId} {
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Profile highlights
    match /profileHighlights/{highlightId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Reactions collection
    match /reactions/{reactionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Engagement metrics
    match /engagementMetrics/{metricId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server writes metrics
    }

    // Stories collection
    match /stories/{storyId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Story views tracking
    match /storyViews/{viewId} {
      allow read: if isAuthenticated() && (
        isOwner(resource.data.viewerId) ||
        isOwner(resource.data.storyUserId)
      );
      allow create: if isAuthenticated() && isOwner(request.resource.data.viewerId);
    }

    // Status updates
    match /statusUpdates/{statusId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Channels (broadcast)
    match /channels/{channelId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.createdBy);
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Channel subscriptions
    match /channelSubscriptions/{subscriptionId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Business profiles
    match /businessProfiles/{businessId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(resource.data.ownerId);
    }

    // Payments
    match /payments/{paymentId} {
      allow read, write: if isAuthenticated() && (
        isOwner(resource.data.senderId) ||
        isOwner(resource.data.receiverId)
      );
    }

    // Wallets
    match /wallets/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Locations
    match /locations/{locationId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(request.resource.data.sharedBy);
      allow update: if isAuthenticated() && isOwner(resource.data.sharedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.sharedBy);
    }

    // Live locations
    match /liveLocations/{locationId} {
      allow read: if isAuthenticated() && (
        isOwner(resource.data.sharedBy) ||
        request.auth.uid in resource.data.sharedWith
      );
      allow write: if isAuthenticated() && isOwner(resource.data.sharedBy);
    }

    // Moderation actions
    match /moderationActions/{actionId} {
      allow read: if false; // Only admin access
      allow write: if false; // Only admin access
    }

    // Chat exports
    match /chatExports/{exportId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Backups
    match /backups/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // User sessions
    match /userSessions/{sessionId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Shared content
    match /sharedContent/{contentId} {
      allow read: if isAuthenticated() && (
        isOwner(resource.data.sharedBy) ||
        request.auth.uid in resource.data.sharedWith
      );
      allow write: if isAuthenticated() && isOwner(resource.data.sharedBy);
    }

    // Navigation state
    match /navigationState/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ===== MISSING CRITICAL COLLECTIONS =====

    // Scroll tracking analytics
    match /scrollTracking/{scrollId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Navigation analytics
    match /navigationAnalytics/{navId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Page views tracking
    match /pageViews/{viewId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Error logs for debugging
    match /errorLogs/{errorId} {
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Feedback and reports
    match /feedback/{feedbackId} {
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Download progress tracking
    match /downloadProgress/{progressId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Download statistics
    match /downloadStats/{statsId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Voice message downloads
    match /voiceDownloads/{downloadId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // Recent activity tracking
    match /recentActivity/{activityId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // User activity logs
    match /activityLogs/{logId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // App usage statistics
    match /usageStats/{statsId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
    }

    // ===== FALLBACK RULES =====

    // Allow authenticated users to access any collection not explicitly denied
    match /{collection}/{document} {
      allow read, write: if isAuthenticated();
    }

    // Default deny rule for any other documents
    match /{document=**} {
      allow read, write: if isAuthenticated();
    }
  }
}
