rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY: Allow all authenticated users to read/write everything
    // This is for development/testing only - NOT for production!
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // Allow unauthenticated read for testing (DEVELOPMENT ONLY)
    match /{document=**} {
      allow read: if true;
    }
  }
}