import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useState } from "react";
import {
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// No mock contacts - will load real contacts from device and Firebase
const mockContacts: any[] = [];

const ContactsScreen = () => {
  const router = useRouter();
  const [searchText, setSearchText] = useState("");
  const [filteredContacts, setFilteredContacts] = useState(mockContacts.filter(contact => contact.hasIraChat));

  const formatLastSeen = (lastSeen: Date | string | undefined): string => {
    try {
      if (!lastSeen) {
        return "Unknown";
      }

      let date: Date;
      if (lastSeen instanceof Date) {
        date = lastSeen;
      } else if (typeof lastSeen === "string") {
        date = new Date(lastSeen);
      } else {
        return "Unknown";
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        return "Unknown";
      }

      const now = new Date();
      const diff = now.getTime() - date.getTime();
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(diff / 3600000);
      const days = Math.floor(diff / 86400000);

      if (minutes < 1) return "just now";
      if (minutes < 60) return `${minutes}m ago`;
      if (hours < 24) return `${hours}h ago`;
      return `${days}d ago`;
    } catch (error) {
      console.error("Error formatting last seen:", error);
      return "Unknown";
    }
  };

  const handleSearch = (text: string) => {
    setSearchText(text);
    if (text.trim() === "") {
      // Only show IraChat users
      setFilteredContacts(mockContacts.filter(contact => contact.hasIraChat));
    } else {
      // Only search among IraChat users
      const filtered = mockContacts
        .filter(contact => contact.hasIraChat)
        .filter((contact) =>
          contact.name.toLowerCase().includes(text.toLowerCase()),
        );
      setFilteredContacts(filtered);
    }
  };

  const openChat = (contact: any) => {
    console.log("🚀 Opening chat with contact:", contact.name);

    // Use replace for faster navigation and immediate feedback
    router.replace({
      pathname: "/individual-chat",
      params: {
        contactId: contact.id,
        contactName: contact.name,
        contactAvatar: contact.avatar,
        contactIsOnline: contact.isOnline.toString(),
        contactLastSeen: contact.lastSeen?.getTime().toString() || "",
      },
    });
  };

  const renderContact = (contact: any, index: number) => {
    return (
      <TouchableOpacity
        key={contact.id}
        onPress={() => openChat(contact)}
        className="flex-row items-center px-4 py-3 border-b border-gray-100"
        activeOpacity={0.7}
      >
        <View style={{ position: 'relative' }}>
          {(() => {
            const Avatar = require("../components/Avatar").Avatar;
            return (
              <Avatar
                name={contact.name}
                imageUrl={contact.avatar}
                size="medium"
                showOnlineStatus={true}
                isOnline={contact.isOnline}
              />
            );
          })()}
        </View>

        <View className="flex-1 ml-3">
          <Text className="text-gray-900 font-semibold text-base">
            {contact.name}
          </Text>
          <View className="flex-row items-center mt-1">
            {contact.isOnline ? (
              <Text className="text-green-600 text-sm font-medium">Online</Text>
            ) : (
              <Text className="text-gray-500 text-sm">
                Last seen {formatLastSeen(contact.lastSeen)}
              </Text>
            )}
          </View>
        </View>

        <View className="items-center">
          <View className="bg-sky-100 rounded-full p-2">
            <Ionicons name="chatbubble" size={16} color="#0ea5e9" />
          </View>
          <Text className="text-xs text-sky-600 mt-1 font-medium">IraChat</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center px-4 py-3 bg-sky-500 shadow-sm">
        <TouchableOpacity onPress={() => router.back()} className="mr-3">
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>

        <View className="flex-1">
          <Text className="text-white font-semibold text-lg">
            Select Contact
          </Text>
          <Text className="text-white/80 text-sm">
            {filteredContacts.length} IraChat users
          </Text>
        </View>
      </View>

      {/* Search Bar */}
      <View className="px-4 py-3 bg-gray-50 border-b border-gray-200">
        <View className="flex-row items-center bg-white rounded-full px-4 py-2 border border-gray-200">
          <Ionicons name="search" size={20} color="#6b7280" />
          <TextInput
            value={searchText}
            onChangeText={handleSearch}
            placeholder="Search contacts..."
            className="flex-1 ml-3 text-base"
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch("")}>
              <Ionicons name="close-circle" size={20} color="#6b7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Contacts List */}
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {filteredContacts.length > 0 ? (
          <>
            <View className="px-4 py-3 bg-gray-50">
              <Text className="text-gray-600 text-sm font-medium">
                CONTACTS ON IRACHAT ({filteredContacts.length})
              </Text>
            </View>

            {filteredContacts.map((contact, index) =>
              renderContact(contact, index),
            )}
          </>
        ) : (
          <View className="flex-1 items-center justify-center px-4">
            <Ionicons name="people-outline" size={64} color="#d1d5db" />
            <Text className="text-gray-500 text-lg font-medium mt-4">
              No contacts found
            </Text>
            <Text className="text-gray-400 text-center mt-2">
              Try searching with a different name
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Invite Friends Button */}
      <View className="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <TouchableOpacity className="flex-row items-center justify-center bg-sky-500 rounded-full py-3">
          <Ionicons name="person-add" size={20} color="white" />
          <Text className="text-white font-semibold ml-2">
            Invite Friends to IraChat
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default ContactsScreen;
